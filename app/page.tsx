"use client"

import { MainLayout } from "@/components/layout/MainLayout"
// import { ThemeDemo } from "@/components/theme/ThemeDemo"
// import { LanguageDemo } from "@/components/demo/LanguageDemo"
// import { ShadcnDemo } from "@/components/demo/ShadcnDemo"
import { useTranslation } from "react-i18next"

export default function Home() {
  const { t } = useTranslation()
  return (
    <MainLayout>
      <div className="space-y-8">
        {/* <ShadcnDemo /> */}
        {/* <ThemeDemo /> */}
        {/* <LanguageDemo /> */}
      </div>
    </MainLayout>
  )
}
