"use client"

import { useEffect, useRef } from "react"
import { useSidebarStore } from "@/stores/sidebarStore"
import { useTranslation } from "react-i18next"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ChevronRight,
  Home,
  User,
  Briefcase,
  TrendingUp,
  FileText,
  Mail,
  Shield,
  Cookie,
  FileCheck
} from "lucide-react"

interface MenuItemProps {
  label: string
  icon?: React.ReactNode
  href?: string
  onClick?: () => void
  showArrow?: boolean
  subItems?: Array<{
    label: string
    href?: string
    onClick?: () => void
  }>
}

function MenuItem({ label, icon, href = "#", onClick, showArrow = true, subItems }: MenuItemProps) {
  const { closeSidebar } = useSidebarStore()

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    // Close sidebar when clicking on a menu item (unless it has sub-items)
    if (!subItems) {
      closeSidebar()
    }
  }

  // If the item has sub-items, render as a dropdown
  if (subItems && subItems.length > 0) {
    return (
      <div className="border-b border-gray-100 dark:border-slate-700">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              type="button"
              className="w-full flex items-center justify-between px-6 py-4
                text-gray-700 dark:text-slate-300
                hover:bg-gray-100 dark:hover:bg-slate-700
                transition-colors duration-200
                text-left"
            >
              <div className="flex items-center gap-3">
                {icon && <span className="w-5 h-5">{icon}</span>}
                <span className="text-base font-medium">{label}</span>
              </div>
              {showArrow && <ChevronRight className="w-5 h-5 text-gray-400 dark:text-slate-500" />}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start" className="w-48">
            {subItems.map((subItem, index) => (
              <DropdownMenuItem
                key={index}
                onClick={() => {
                  if (subItem.onClick) {
                    subItem.onClick()
                  }
                  closeSidebar()
                }}
                className="cursor-pointer"
              >
                {subItem.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    )
  }

  // Regular menu item without sub-items
  return (
    <a
      href={href}
      onClick={handleClick}
      className="flex items-center justify-between px-6 py-4
        text-gray-700 dark:text-slate-300
        hover:bg-gray-100 dark:hover:bg-slate-700
        transition-colors duration-200
        border-b border-gray-100 dark:border-slate-700"
    >
      <div className="flex items-center gap-3">
        {icon && <span className="w-5 h-5">{icon}</span>}
        <span className="text-base font-medium">{label}</span>
      </div>
      {showArrow && <ChevronRight className="w-5 h-5 text-gray-400 dark:text-slate-500" />}
    </a>
  )
}

export function SideMenuBar() {
  const { isOpen, closeSidebar } = useSidebarStore()
  const { t } = useTranslation()
  const sidebarRef = useRef<HTMLDivElement>(null)

  // Close sidebar when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        closeSidebar()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      // Prevent body scroll when sidebar is open
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = "unset"
    }
  }, [isOpen, closeSidebar])

  // Close sidebar on escape key
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === "Escape") {
        closeSidebar()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape)
    }

    return () => {
      document.removeEventListener("keydown", handleEscape)
    }
  }, [isOpen, closeSidebar])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-999 transition-opacity duration-300"
        onClick={closeSidebar}
      />

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={`fixed top-0 left-0 h-full w-80 z-50
          bg-white-50 dark:bg-dark-secondary-600
          shadow-xl transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
          flex flex-col`}
      >
        {/* Header with close button */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-slate-100">
            Menu
          </h2>
          <button
            type="button"
            onClick={closeSidebar}
            className="p-2 rounded-lg text-gray-600 dark:text-slate-400
              hover:text-gray-900 dark:hover:text-slate-100
              hover:bg-gray-100 dark:hover:bg-slate-700
              transition-colors duration-200"
            aria-label="Close sidebar"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Navigation Menu Items */}
        <div className="flex-1 overflow-y-auto">
          <nav className="py-2">
            <MenuItem
              label={t("navigation.home")}
              icon={<Home />}
            />
            <MenuItem
              label={t("navigation.about")}
              icon={<User />}
            />
            <MenuItem
              label={t("navigation.services")}
              icon={<Briefcase />}
              subItems={[
                { label: "Web Development", href: "/services/web" },
                { label: "Mobile Apps", href: "/services/mobile" },
                { label: "Consulting", href: "/services/consulting" },
                { label: "Support", href: "/services/support" }
              ]}
            />
            <MenuItem
              label="Market Insights"
              icon={<TrendingUp />}
              subItems={[
                { label: "Market Analysis", href: "/insights/analysis" },
                { label: "Trends", href: "/insights/trends" },
                { label: "Reports", href: "/insights/reports" }
              ]}
            />
            <MenuItem
              label="Blog"
              icon={<FileText />}
            />
            <MenuItem
              label="Contact"
              icon={<Mail />}
            />

            {/* Separator line */}
            <div className="my-4 border-t border-gray-200 dark:border-slate-700" />

            {/* Footer menu items */}
            <MenuItem
              label="Terms of use"
              icon={<FileCheck />}
            />
            <MenuItem
              label="Privacy Policy"
              icon={<Shield />}
              showArrow={false}
            />
            <MenuItem
              label="Security Policy"
              icon={<Shield />}
              showArrow={false}
            />
            <MenuItem
              label="Cookie Settings"
              icon={<Cookie />}
              showArrow={false}
            />
          </nav>
        </div>

        {/* Investment Button */}
        <div className="p-6 border-t border-gray-200 dark:border-slate-700">
          <button
            type="button"
            className="w-full py-3 px-4
              bg-primary-500 hover:bg-primary-600
              text-white font-semibold rounded-lg
              transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            onClick={closeSidebar}
          >
            Investment
          </button>
        </div>
      </div>
    </>
  )
}