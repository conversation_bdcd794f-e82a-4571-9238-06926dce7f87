"use client"

import { LanguageToggle } from "@/components/common/LanguageToggle"
import { ThemeToggle } from "@/components/common/ThemeToggle"
import { SideMenuBar } from "./sidemenubar"
import { useSidebarStore } from "@/stores/sidebarStore"
import { Icon } from "@/components/common/Icon"
import { useTranslation } from "react-i18next"

export function Header() {
  const { t } = useTranslation()
  const { toggleSidebar } = useSidebarStore()

  return (
    <>
      <header className="fixed top-0 z-50 w-full bg-transparent min-h-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="grid grid-cols-12 h-24 gap-4">
            <SideMenuBar />

            {/* Sidebar Button - 1 column */}
            <div className="col-span-1 flex items-center">
              <button
                type="button"
                onClick={toggleSidebar}
                className="p-2 rounded-lg text-primary-50 
                  hover:text-primary-50  hover:bg-button-background-icon 
                  transition-colors duration-200"
                aria-label="Open sidebar"
              >
                <Icon name="arrow-menu-alt-left" size={24} />
              </button>
            </div>

            {/* Navigation with Logo - 8 columns */}
            <nav className="col-span-9 hidden md:grid grid-cols-5 items-center gap-4">
              <a
                href="#"
                className="text-primary-400 hover:text-primary-400 active:text-primary-400
                transition-colors duration-200 font-medium text-center"
              >
                {t("navigation.home")}
              </a>
              <a
                href="#"
                className="text-primary-50 hover:text-primary-400 active:text-primary-400
                transition-colors duration-200 font-medium text-center"
              >
                {t("navigation.about")}
              </a>

              {/* Logo/Brand - Center */}
              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 dark:from-blue-500 dark:to-blue-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-lg">S</span>
                  </div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-slate-100">
                    Swag
                  </h1>
                </div>
              </div>

              <a
                href="#"
                className="text-primary-50 hover:text-primary-400 active:text-primary-400
                transition-colors duration-200 font-medium text-center"
              >
                {t("navigation.services")}
              </a>
              <a
                href="#"
                className="text-primary-50 hover:text-primary-400 active:text-primary-400
                transition-colors duration-200 font-medium text-center"
              >
                Market Insights
              </a>
            </nav>

            {/* Right side actions - 3 columns */}
            <div className="col-span-2 flex items-center justify-end space-x-4 rtl:space-x-reverse">
              {/* Theme toggle */}
              <ThemeToggle />

              {/* Language toggle */}
              <LanguageToggle />
            </div>
          </div>
        </div>
      </header>
    </>
  )
}
